spring.neo4j.uri=bolt://localhost:7687
spring.neo4j.authentication.username=neo4j
spring.neo4j.authentication.password=K@kapoDev@123
spring.neo4j.ssl.trust.strategy=TRUST_ALL_CERTIFICATES
org.springframework.data.neo4j=DEBUG

springdoc.api-docs.path=/apidocs
spring.profiles.active=dev
spring.messages.basename=lang/messages

json.files.location=./json-files
pdf.files.location=./pdf-files
logging.level.root=INFO
logging.level.kakapo=INFO

#Password Policy
stage-server.password.max-length=20
stage-server.password.min-length=6
stage-server.password.min-special-char=0
stage-server.password.min-uppercase=0

spring.http.multipart.max-file-size=10MB
spring.http.multipart.max-request-size=10MB
spring.http.multipart.enabled=true
spring.http.multipart.location= ../uploads

# Email/SMS Tokens expiration time in minutes
stage-server.token.expiration-time=15

# Two Factor Authentication Prompt
stage-server.two-factor-authentication-prompt=true
stage-server-sms-code-length=6

# Time in minutes
virtual-profile-booking-cancel-time=30

stage-server-front-end-url=http://localhost:3000
stage-server-back-end-url=http://localhost:8080
# ACT Profile items
stage-server.act.performing-languages=English, French, French(CA),Italian, German, Don't Care

spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=stsgvrgzqjcirdmd
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

app.jwt-secret=5040841344278d3586387df5e511bbe7504353f3380df26c8011bc6957014453
app.jwt-expiration-milliseconds=108000000
google.api.key=AIzaSyCcinvHKlp-sBxSE0Vz1NQFA3msyVQlQuY

# Test mode configuration - enables automatic authentication for development
stage-server.test-mode.enabled=false
stage-server.test-mode.default-user=<EMAIL>
stage-server.test-mode.default-role=USER


spring.security.oauth2.client.registration.google.client-id=769596721332-0uuqrst3seb556bgngjomcj7ep01tmm2.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.client-secret=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


stripe.payment.secret.key=sk_test_51R8DTOPJdFr44Flk23nuChSlDAF7A1NOMEFJ5PLKC8SvsgkvnIq47NvAlJPDN4lvlDxjJn9JKHUn9XlWupUhPNDw00mfc8pbC1
stripe.webhook.secret=whsec_CH2O8QU04MwsS57Q1rdhUDXWjZNKYns8
stage-server-stripe-payment-amount=1000
stage-server-stripe-payment-currency=CAD
stage-server-stripe-payment-name=StageMinder Payment
stage-server-stripe-payment-quantity=1

aws.region=us-east-2
aws.access-key=********************
aws.secret-key=wPMYat7JS1zMBjqK836gNHeluuziQp80vlLCi1Hn
aws.s3.bucket-name=hbp2
