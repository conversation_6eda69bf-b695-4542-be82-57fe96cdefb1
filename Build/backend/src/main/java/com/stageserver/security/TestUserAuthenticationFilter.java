package com.stageserver.security;

import com.stageserver.repository.UserRepository;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;

/**
 * Test authentication filter that automatically authenticates a default test user
 * for development and testing purposes. This bypasses normal JWT authentication.
 */
@Slf4j
@Component
public class TestUserAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private UserRepository userRepository;

    @Value("${stage-server.test-mode.enabled:false}")
    private boolean testModeEnabled;

    @Value("${stage-server.test-mode.default-user:<EMAIL>}")
    private String defaultTestUser;

    @Value("${stage-server.test-mode.default-role:USER}")
    private String defaultTestRole;

    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        // Only apply test authentication if test mode is enabled and no authentication exists
        if (testModeEnabled && SecurityContextHolder.getContext().getAuthentication() == null) {
            
            // Skip authentication for public endpoints
            String requestPath = request.getRequestURI();
            if (isPublicEndpoint(requestPath)) {
                filterChain.doFilter(request, response);
                return;
            }
            
            log.debug("Test mode enabled - automatically authenticating user: {}", defaultTestUser);

            // Create test user details using UserRegistrationDetails
            UserDetails testUserDetails = userRepository.findByEmail(defaultTestUser)
                .map(UserRegistrationDetails::new)
                .orElse(null);

            if (testUserDetails == null) {
                log.warn("Test user {} not found in database, skipping authentication", defaultTestUser);
                filterChain.doFilter(request, response);
                return;
            }
            
            // Create authentication token
            UsernamePasswordAuthenticationToken authenticationToken = 
                new UsernamePasswordAuthenticationToken(
                    testUserDetails, 
                    null, 
                    testUserDetails.getAuthorities()
                );
            
            authenticationToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
            SecurityContextHolder.getContext().setAuthentication(authenticationToken);
            
            log.debug("Test user {} authenticated with role {}", defaultTestUser, defaultTestRole);
        }
        
        filterChain.doFilter(request, response);
    }
    
    /**
     * Check if the request path is for a public endpoint that doesn't need authentication
     */
    private boolean isPublicEndpoint(String requestPath) {
        return requestPath.startsWith("/api/v1/public/") ||
               requestPath.startsWith("/ws/") ||
               requestPath.startsWith("/swagger-ui/") ||
               requestPath.startsWith("/apidocs/") ||
               requestPath.startsWith("/oauth2/") ||
               requestPath.startsWith("/actuator/") ||
               requestPath.startsWith("/reset-password/");
    }
}
