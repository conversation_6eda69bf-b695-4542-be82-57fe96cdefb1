@echo off
echo ========================================
echo 测试 StageMinder 登录功能
echo ========================================
echo.

echo 1. 检查前端开发模式配置...
echo 查看 .env 文件中的开发模式设置:
echo.
type "Build\frontend\.env"
echo.
echo ========================================

echo 2. 检查后端测试模式配置...
echo 查看 application.properties 中的测试模式设置:
echo.
findstr "stage-server.test-mode" "Build\backend\src\main\resources\application.properties"
echo.
echo ========================================

echo 3. 测试后端认证API...
echo 发送测试请求到: http://localhost:8080/api/v1/private/users/token-info
echo 这应该在测试模式下自动认证 <EMAIL>
echo.

curl -X GET http://localhost:8080/api/v1/private/users/token-info ^
  -H "Content-Type: application/json" ^
  -w "\nHTTP Status: %%{http_code}\n"

echo.
echo ========================================

echo 4. 测试获取当前用户信息...
echo 发送测试请求到: http://localhost:8080/api/v1/private/users/current
echo.

curl -X GET http://localhost:8080/api/v1/private/users/current ^
  -H "Content-Type: application/json" ^
  -w "\nHTTP Status: %%{http_code}\n"

echo.
echo ========================================

echo 5. 测试登录API端点...
echo 发送登录请求到: http://localhost:8080/api/v1/public/login
echo.

curl -X POST http://localhost:8080/api/v1/public/login ^
  -H "Content-Type: application/json" ^
  -d "{\"email\":\"<EMAIL>\",\"password\":\"testpassword\"}" ^
  -w "\nHTTP Status: %%{http_code}\n"

echo.
echo ========================================

echo 测试结果说明:
echo - 如果看到 HTTP Status: 200，说明API端点工作正常
echo - 如果看到 HTTP Status: 401，说明认证失败
echo - 如果看到 HTTP Status: 404，说明端点不存在
echo - 如果看到连接错误，请确保后端服务器正在运行
echo.
echo 要启动应用程序:
echo 1. 后端: 在 Build/backend 目录运行 'mvn spring-boot:run'
echo 2. 前端: 在 Build/frontend 目录运行 'npm run dev'
echo.
pause
